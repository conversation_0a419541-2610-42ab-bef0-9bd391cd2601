/**
 * produtos.js - Auditoria Fiscal
 * Funções para gerenciar a página de produtos
 */

document.addEventListener('DOMContentLoaded', function () {
  // Verificar se estamos na página de produtos
  if (window.location.pathname === '/produto') {
    setupProdutosPage();
  }
});

/**
 * Configura a página de produtos
 */
function setupProdutosPage() {
  // Carregar o conteúdo da página
  loadProdutosContent();

  // Configurar evento para o botão de novo produto
  document.addEventListener('click', function (event) {
    if (
      event.target.id === 'new-produto-btn' ||
      (event.target.parentElement &&
        event.target.parentElement.id === 'new-produto-btn')
    ) {
      createNewProduto();
    }
  });
}

/**
 * Carrega o conteúdo da página de produtos
 */
function loadProdutosContent() {
  const pageContent = document.getElementById('page-content');
  if (!pageContent) return;

  // Verificar se a seção já existe
  let produtosSection = document.getElementById('page-produto');
  if (produtosSection) {
    produtosSection.classList.add('active');
    loadProdutoData();
    return;
  }

  // Criar a seção de produtos
  produtosSection = document.createElement('div');
  produtosSection.id = 'page-produto';
  produtosSection.className = 'page-section active';

  // Conteúdo HTML da página
  produtosSection.innerHTML = `
    <div class="section-header">
      <h2><i class="fas fa-box"></i> Produtos</h2>
    </div>

    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h5 class="card-title">Lista de Produtos</h5>
          <div class="d-flex">
            <button id="new-produto-btn" class="btn btn-sm btn-primary">
              <i class="fas fa-plus"></i> Novo Produto
            </button>
          </div>
        </div>

        <div id="produtos-content">
          <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Carregando...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Adicionar a seção ao conteúdo da página
  pageContent.appendChild(produtosSection);

  // Carregar dados de produtos
  loadProdutoData();
}

// Função de filtros removida conforme solicitado

/**
 * Carrega os dados de produtos com suporte a paginação
 * @param {number} page - Número da página a ser carregada (padrão: 1)
 * @param {number} perPage - Número de itens por página (padrão: 100)
 */
function loadProdutoData(page = 1, perPage = 100) {
  const produtosContent = document.getElementById('produtos-content');
  if (!produtosContent) return;

  // Mostrar indicador de carregamento
  produtosContent.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
      <p class="mt-2">Carregando dados, por favor aguarde...</p>
    </div>
  `;

  // Parâmetros de filtro
  const params = new URLSearchParams();
  if (selectedCompany) {
    params.append('empresa_id', selectedCompany);
  }

  // Adicionar parâmetros de paginação
  params.append('page', page);
  params.append('per_page', perPage); // Número de itens por página

  // Status filter removed as requested

  // Obter produtos da API
  fetch(`/api/produtos?${params.toString()}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.produtos && data.produtos.length > 0) {
        // Renderizar tabela de produtos
        renderProdutosTable(produtosContent, data.produtos, data.pagination);
      } else {
        // Mostrar mensagem de nenhum produto
        produtosContent.innerHTML = `
          <div class="alert alert-info">
            Nenhum produto encontrado.
          </div>
        `;
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar produtos:', error);
      produtosContent.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar produtos: ${error.message}
        </div>
      `;
    });
}

/**
 * Renderiza a tabela de produtos com suporte a paginação
 * @param {HTMLElement} container - Container onde a tabela será renderizada
 * @param {Array} produtos - Lista de produtos
 * @param {Object} pagination - Informações de paginação
 */
function renderProdutosTable(container, produtos, pagination) {
  // Criar controles de paginação se houver informações de paginação
  let paginationControls = '';
  if (pagination) {
    paginationControls = `
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
          <span class="text-muted me-3">Mostrando ${pagination.from} a ${
      pagination.to
    } de ${pagination.total} registros</span>
          <select id="per-page-select" class="form-select form-select-sm" style="width: auto;">
            <option value="50" ${
              pagination.per_page == 50 ? 'selected' : ''
            }>50 por página</option>
            <option value="100" ${
              pagination.per_page == 100 ? 'selected' : ''
            }>100 por página</option>
            <option value="200" ${
              pagination.per_page == 200 ? 'selected' : ''
            }>200 por página</option>
            <option value="500" ${
              pagination.per_page == 500 ? 'selected' : ''
            }>500 por página</option>
          </select>
        </div>
        <nav aria-label="Navegação de páginas">
          <ul class="pagination mb-0">
            <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
              <a class="page-link" href="#" data-page="1" aria-label="Primeira">
                <span aria-hidden="true">&laquo;&laquo;</span>
              </a>
            </li>
            <li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
              <a class="page-link" href="#" data-page="${
                pagination.prev_page || 1
              }" aria-label="Anterior">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
            ${getPaginationLinks(pagination.current_page, pagination.last_page)}
            <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
              <a class="page-link" href="#" data-page="${
                pagination.next_page || pagination.last_page
              }" aria-label="Próxima">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
            <li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
              <a class="page-link" href="#" data-page="${
                pagination.last_page
              }" aria-label="Última">
                <span aria-hidden="true">&raquo;&raquo;</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    `;
  }

  // Criar HTML da tabela
  let html = `
    <div class="table-responsive">
      <table id="produtos-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>Código</th>
            <th>Descrição</th>
            <th>Unidade</th>
            <th>NCM</th>
            <th>CFOP</th>
            <th>cEAN</th>
            <th>CEST</th>
            <th>Ações</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas da tabela
  produtos.forEach((produto) => {
    html += `
      <tr>
        <td>${produto.codigo}</td>
        <td>${produto.descricao}</td>
        <td>${produto.unidade_comercial || '-'}</td>
        <td>${produto.ncm || '-'}</td>
        <td>${produto.cfop || '-'}</td>
        <td>${produto.codigo_ean || '-'}</td>
        <td>${produto.cest || '-'}</td>
        <td>
          <div class="btn-group">
            <button class="btn btn-sm btn-primary edit-produto-btn" data-id="${
              produto.id
            }">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger delete-produto-btn" data-id="${
              produto.id
            }" data-nome="${produto.descricao}">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
    ${paginationControls}
  `;

  // Atualizar o conteúdo
  container.innerHTML = html;

  // Inicializar DataTable com paginação desativada (usamos nossa própria paginação no servidor)
  try {
    new DataTable('#produtos-table', {
      language: {
        url: '/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
      paging: false,
      lengthChange: false,
      info: false,
    });
  } catch (error) {
    console.error('Erro ao inicializar DataTable:', error);
  }

  // Configurar botões de ação
  setupProdutoButtons();

  // Configurar eventos para os links de paginação e seletor de itens por página
  if (pagination) {
    setupPaginationEvents();
    setupPerPageSelect();
  }
}

/**
 * Gera os links de paginação
 * @param {number} currentPage - Página atual
 * @param {number} lastPage - Última página
 * @returns {string} HTML com os links de paginação
 */
function getPaginationLinks(currentPage, lastPage) {
  let links = '';
  const maxLinks = 5; // Número máximo de links a serem exibidos

  // Determinar o intervalo de páginas a serem exibidas
  let startPage = Math.max(1, currentPage - Math.floor(maxLinks / 2));
  let endPage = Math.min(lastPage, startPage + maxLinks - 1);

  // Ajustar o intervalo se necessário
  if (endPage - startPage + 1 < maxLinks && startPage > 1) {
    startPage = Math.max(1, endPage - maxLinks + 1);
  }

  // Gerar os links
  for (let i = startPage; i <= endPage; i++) {
    links += `
      <li class="page-item ${i === currentPage ? 'active' : ''}">
        <a class="page-link" href="#" data-page="${i}">${i}</a>
      </li>
    `;
  }

  return links;
}

/**
 * Configura eventos para os links de paginação
 */
function setupPaginationEvents() {
  // Adicionar evento de clique para os links de paginação
  document.querySelectorAll('.pagination .page-link').forEach((link) => {
    link.addEventListener('click', function (event) {
      event.preventDefault();
      const page = parseInt(this.getAttribute('data-page'));
      if (!isNaN(page)) {
        // Carregar a página selecionada
        const perPage =
          document.getElementById('per-page-select')?.value || 100;
        loadProdutoData(page, perPage);
      }
    });
  });
}

/**
 * Configura o seletor de itens por página
 */
function setupPerPageSelect() {
  const perPageSelect = document.getElementById('per-page-select');
  if (perPageSelect) {
    perPageSelect.addEventListener('change', function () {
      // Ao mudar a quantidade de itens por página, voltar para a primeira página
      loadProdutoData(1, this.value);
    });
  }
}

/**
 * Configura os botões de ação da tabela de produtos
 */
function setupProdutoButtons() {
  // Botão de editar produto
  document.querySelectorAll('.edit-produto-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const produtoId = this.getAttribute('data-id');
      editProduto(produtoId);
    });
  });

  // Botão de excluir produto
  document.querySelectorAll('.delete-produto-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const produtoId = this.getAttribute('data-id');
      const produtoNome = this.getAttribute('data-nome');
      deleteProduto(produtoId, produtoNome);
    });
  });
}

/**
 * Exclui um produto
 * @param {number} produtoId - ID do produto
 * @param {string} produtoNome - Nome do produto para exibir na confirmação
 */
function deleteProduto(produtoId, produtoNome) {
  // Confirmar exclusão
  if (
    !confirm(
      `Tem certeza que deseja excluir o produto "${produtoNome}"? Esta ação não pode ser desfeita.`,
    )
  ) {
    return;
  }

  // Enviar requisição para excluir o produto
  fetch(`/api/produtos/${produtoId}`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.message) {
        alert(data.message);
        // Recarregar os dados de produtos
        loadProdutoData();
      } else {
        alert('Erro ao excluir produto');
      }
    })
    .catch((error) => {
      console.error('Erro ao excluir produto:', error);
      alert('Erro ao excluir produto');
    });
}

/**
 * Edita um produto
 */
function editProduto(produtoId) {
  // Obter detalhes do produto da API
  fetch(`/api/produtos/${produtoId}`, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.produto) {
        // Criar modal para editar o produto
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'edit-produto-modal';
        modal.tabIndex = '-1';
        modal.setAttribute('aria-labelledby', 'edit-produto-modal-label');
        modal.setAttribute('aria-hidden', 'true');

        const produto = data.produto;

        modal.innerHTML = `
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="edit-produto-modal-label">Editar Produto</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
              </div>
              <div class="modal-body">
                <form id="edit-produto-form">
                  <ul class="nav nav-tabs" id="editProdutoTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                      <button class="nav-link active" id="edit-info-tab" data-bs-toggle="tab" data-bs-target="#edit-info-content" type="button" role="tab" aria-controls="edit-info-content" aria-selected="true">
                        Informações Gerais
                      </button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="edit-fiscal-tab" data-bs-toggle="tab" data-bs-target="#edit-fiscal-content" type="button" role="tab" aria-controls="edit-fiscal-content" aria-selected="false">
                        Informações Fiscais
                      </button>
                    </li>
                  </ul>

                  <div class="tab-content mt-3" id="editProdutoTabsContent">
                    <!-- Tab Informações Gerais -->
                    <div class="tab-pane fade show active" id="edit-info-content" role="tabpanel" aria-labelledby="edit-info-tab">
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="codigo" class="form-label">Código *</label>
                          <input type="text" class="form-control" id="codigo" name="codigo" value="${
                            produto.codigo
                          }" required>
                        </div>
                        <div class="col-md-6">
                          <label for="descricao" class="form-label">Descrição *</label>
                          <input type="text" class="form-control" id="descricao" name="descricao" value="${
                            produto.descricao
                          }" required>
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="unidade_comercial" class="form-label">Unidade Comercial</label>
                          <input type="text" class="form-control" id="unidade_comercial" name="unidade_comercial" value="${
                            produto.unidade_comercial || ''
                          }">
                        </div>
                        <div class="col-md-6">
                          <label for="unidade_tributavel" class="form-label">Unidade Tributável</label>
                          <input type="text" class="form-control" id="unidade_tributavel" name="unidade_tributavel" value="${
                            produto.unidade_tributavel || ''
                          }">
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="unidade_tributaria" class="form-label">Unidade Tributária</label>
                          <input type="text" class="form-control" id="unidade_tributaria" name="unidade_tributaria" value="${
                            produto.unidade_tributaria || ''
                          }">
                        </div>
                        <div class="col-md-6">
                          <label for="tipo_sped" class="form-label">Tipo SPED</label>
                          <input type="text" class="form-control" id="tipo_sped" name="tipo_sped" value="${
                            produto.tipo_sped || ''
                          }">
                        </div>
                      </div>
                    </div>

                    <!-- Tab Informações Fiscais -->
                    <div class="tab-pane fade" id="edit-fiscal-content" role="tabpanel" aria-labelledby="edit-fiscal-tab">
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="ncm" class="form-label">NCM (somente leitura)</label>
                          <input type="text" class="form-control" id="ncm" name="ncm" value="${
                            produto.ncm || ''
                          }" readonly>
                          <small class="text-muted">O NCM é obtido das notas fiscais e cenários</small>
                        </div>
                        <div class="col-md-6">
                          <label for="cfop" class="form-label">CFOP (somente leitura)</label>
                          <input type="text" class="form-control" id="cfop" name="cfop" value="${
                            produto.cfop || ''
                          }" readonly>
                          <small class="text-muted">O CFOP é obtido das notas fiscais e cenários</small>
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-md-6">
                          <label for="codigo_ean" class="form-label">Código EAN</label>
                          <input type="text" class="form-control" id="codigo_ean" name="codigo_ean" value="${
                            produto.codigo_ean || ''
                          }">
                        </div>
                        <div class="col-md-6">
                          <label for="codigo_ean_tributavel" class="form-label">Código EAN Tributável</label>
                          <input type="text" class="form-control" id="codigo_ean_tributavel" name="codigo_ean_tributavel" value="${
                            produto.codigo_ean_tributavel || ''
                          }">
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="update-produto-btn">Salvar</button>
              </div>
            </div>
          </div>
        `;

        // Adicionar o modal ao body
        document.body.appendChild(modal);

        // Inicializar o modal
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();

        // Configurar evento para remover o modal quando for fechado
        modal.addEventListener('hidden.bs.modal', function () {
          document.body.removeChild(modal);
        });

        // Configurar botão de salvar
        const updateBtn = modal.querySelector('#update-produto-btn');
        if (updateBtn) {
          updateBtn.addEventListener('click', function () {
            // Validar o formulário
            const form = modal.querySelector('#edit-produto-form');
            if (!form.checkValidity()) {
              form.reportValidity();
              return;
            }

            // Obter os dados do formulário
            const formData = {
              codigo: modal.querySelector('#codigo').value,
              descricao: modal.querySelector('#descricao').value,
              unidade_comercial:
                modal.querySelector('#unidade_comercial').value,
              unidade_tributavel: modal.querySelector('#unidade_tributavel')
                .value,
              unidade_tributaria: modal.querySelector('#unidade_tributaria')
                .value,
              tipo_sped: modal.querySelector('#tipo_sped').value,
              codigo_ean: modal.querySelector('#codigo_ean').value,
              codigo_ean_tributavel: modal.querySelector(
                '#codigo_ean_tributavel',
              ).value,
              cest: modal.querySelector('#cest').value,
              // Não incluir NCM e CFOP, pois são somente leitura
            };

            // Enviar os dados para a API
            fetch(`/api/produtos/${produtoId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${localStorage.getItem('token')}`,
              },
              body: JSON.stringify(formData),
            })
              .then((response) => response.json())
              .then((data) => {
                if (data.message) {
                  alert(data.message);
                  // Fechar o modal
                  modalInstance.hide();
                  // Recarregar os dados de produtos
                  loadProdutoData();
                } else {
                  alert('Erro ao atualizar produto');
                }
              })
              .catch((error) => {
                console.error('Erro ao atualizar produto:', error);
                alert('Erro ao atualizar produto');
              });
          });
        }
      } else {
        alert('Erro ao carregar detalhes do produto');
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar detalhes do produto:', error);
      alert('Erro ao carregar detalhes do produto');
    });
}

/**
 * Cria um modal para exibir os detalhes de um produto
 */
function createProdutoModal(produto) {
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'produto-modal';
  modal.tabIndex = '-1';
  modal.setAttribute('aria-labelledby', 'produto-modal-label');
  modal.setAttribute('aria-hidden', 'true');

  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="produto-modal-label">Detalhes do Produto</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs" id="produtoTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info-content" type="button" role="tab" aria-controls="info-content" aria-selected="true">
                Informações Gerais
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="fiscal-tab" data-bs-toggle="tab" data-bs-target="#fiscal-content" type="button" role="tab" aria-controls="fiscal-content" aria-selected="false">
                Informações Fiscais
              </button>
            </li>
          </ul>

          <div class="tab-content mt-3" id="produtoTabsContent">
            <!-- Tab Informações Gerais -->
            <div class="tab-pane fade show active" id="info-content" role="tabpanel" aria-labelledby="info-tab">
              <div class="row">
                <div class="col-md-6">
                  <p><strong>ID:</strong> ${produto.id}</p>
                  <p><strong>Código:</strong> ${produto.codigo}</p>
                  <p><strong>Descrição:</strong> ${produto.descricao}</p>
                  <p><strong>Status:</strong> ${
                    produto.status === 'producao'
                      ? 'Produção'
                      : produto.status === 'inconsistente'
                      ? 'Inconsistente'
                      : produto.status === 'conforme'
                      ? 'Conforme'
                      : 'Novo'
                  }</p>
                  <p><strong>Data Cadastro:</strong> ${
                    produto.data_cadastro
                      ? new Date(produto.data_cadastro).toLocaleDateString(
                          'pt-BR',
                        )
                      : '-'
                  }</p>
                </div>
                <div class="col-md-6">
                  <p><strong>Unidade Comercial:</strong> ${
                    produto.unidade_comercial || '-'
                  }</p>
                  <p><strong>Unidade Tributável:</strong> ${
                    produto.unidade_tributavel || '-'
                  }</p>
                  <p><strong>Unidade Tributária:</strong> ${
                    produto.unidade_tributaria || '-'
                  }</p>
                </div>
              </div>

              <!-- Tab Informações Fiscais -->
              <div class="tab-pane fade" id="fiscal-content" role="tabpanel" aria-labelledby="fiscal-tab">
                <div class="row">
                  <div class="col-md-6">
                    <p><strong>NCM:</strong> ${produto.ncm || '-'}</p>
                    <p><strong>CFOP:</strong> ${produto.cfop || '-'}</p>
                  </div>
                  <div class="col-md-6">
                    <p><strong>Código EAN:</strong> ${produto.codigo_ean || '-'}</p>
                    <p><strong>Código EAN Tributável:</strong> ${produto.codigo_ean_tributavel || '-'}</p>
                    <p><strong>CEST:</strong> ${produto.cest || '-'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
          ${
            produto.status !== 'producao'
              ? `<button type="button" class="btn btn-success set-producao-modal-btn" data-id="${produto.id}">
                  <i class="fas fa-check"></i> Definir como Produção
                </button>`
              : ''
          }
        </div>
      </div>
    </div>
  `;

  // Configurar botão de definir como produção
  setTimeout(() => {
    const setProducaoBtn = modal.querySelector('.set-producao-modal-btn');
    if (setProducaoBtn) {
      setProducaoBtn.addEventListener('click', function () {
        const produtoId = this.getAttribute('data-id');
        setProdutoProducao(produtoId);

        // Fechar o modal
        const modalInstance = bootstrap.Modal.getInstance(modal);
        modalInstance.hide();
      });
    }
  }, 100);

  return modal;
}

/**
 * Define um produto como produção
 */
function setProdutoProducao(produtoId) {
  if (!confirm('Deseja realmente definir este produto como produção?')) {
    return;
  }

  // Atualizar o status do produto na API
  fetch(`/api/produtos/${produtoId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: JSON.stringify({
      status: 'producao',
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.message) {
        alert(data.message);
        // Recarregar os dados de produtos
        loadProdutoData();
      } else {
        alert('Erro ao atualizar status do produto');
      }
    })
    .catch((error) => {
      console.error('Erro ao atualizar status do produto:', error);
      alert('Erro ao atualizar status do produto');
    });
}

/**
 * Cria um novo produto
 */
function createNewProduto() {
  // Criar modal para o formulário de novo produto
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'new-produto-modal';
  modal.tabIndex = '-1';
  modal.setAttribute('aria-labelledby', 'new-produto-modal-label');
  modal.setAttribute('aria-hidden', 'true');

  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="new-produto-modal-label">Novo Produto</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <form id="new-produto-form">
            <ul class="nav nav-tabs" id="newProdutoTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="new-info-tab" data-bs-toggle="tab" data-bs-target="#new-info-content" type="button" role="tab" aria-controls="new-info-content" aria-selected="true">
                  Informações Gerais
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="new-fiscal-tab" data-bs-toggle="tab" data-bs-target="#new-fiscal-content" type="button" role="tab" aria-controls="new-fiscal-content" aria-selected="false">
                  Informações Fiscais
                </button>
              </li>
            </ul>

            <div class="tab-content mt-3" id="newProdutoTabsContent">
              <!-- Tab Informações Gerais -->
              <div class="tab-pane fade show active" id="new-info-content" role="tabpanel" aria-labelledby="new-info-tab">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="codigo" class="form-label">Código *</label>
                    <input type="text" class="form-control" id="codigo" name="codigo" required>
                  </div>
                  <div class="col-md-6">
                    <label for="descricao" class="form-label">Descrição *</label>
                    <input type="text" class="form-control" id="descricao" name="descricao" required>
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="unidade_comercial" class="form-label">Unidade Comercial</label>
                    <input type="text" class="form-control" id="unidade_comercial" name="unidade_comercial">
                  </div>
                  <div class="col-md-6">
                    <label for="unidade_tributavel" class="form-label">Unidade Tributável</label>
                    <input type="text" class="form-control" id="unidade_tributavel" name="unidade_tributavel">
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="unidade_tributaria" class="form-label">Unidade Tributária</label>
                    <input type="text" class="form-control" id="unidade_tributaria" name="unidade_tributaria">
                  </div>
                  <div class="col-md-6">
                    <label for="tipo_sped" class="form-label">Tipo SPED</label>
                    <input type="text" class="form-control" id="tipo_sped" name="tipo_sped">
                  </div>
                </div>
              </div>

              <!-- Tab Informações Fiscais -->
              <div class="tab-pane fade" id="new-fiscal-content" role="tabpanel" aria-labelledby="new-fiscal-tab">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="codigo_ean" class="form-label">Código EAN</label>
                    <input type="text" class="form-control" id="codigo_ean" name="codigo_ean">
                  </div>
                  <div class="col-md-6">
                    <label for="codigo_ean_tributavel" class="form-label">Código EAN Tributável</label>
                    <input type="text" class="form-control" id="codigo_ean_tributavel" name="codigo_ean_tributavel">
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="cest" class="form-label">CEST</label>
                    <input type="text" class="form-control" id="cest" name="cest">
                  </div>
                </div>
                <div class="alert alert-info">
                  <i class="fas fa-info-circle"></i> Os campos NCM e CFOP serão preenchidos automaticamente a partir das notas fiscais importadas.
                </div>

              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" class="btn btn-primary" id="save-produto-btn">Salvar</button>
        </div>
      </div>
    </div>
  `;

  // Adicionar o modal ao body
  document.body.appendChild(modal);

  // Inicializar o modal
  const modalInstance = new bootstrap.Modal(modal);
  modalInstance.show();

  // Configurar evento para remover o modal quando for fechado
  modal.addEventListener('hidden.bs.modal', function () {
    document.body.removeChild(modal);
  });

  // Configurar botão de salvar
  const saveBtn = modal.querySelector('#save-produto-btn');
  if (saveBtn) {
    saveBtn.addEventListener('click', function () {
      // Validar o formulário
      const form = modal.querySelector('#new-produto-form');
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }

      // Obter os dados do formulário
      const formData = {
        codigo: modal.querySelector('#codigo').value,
        descricao: modal.querySelector('#descricao').value,
        unidade_comercial: modal.querySelector('#unidade_comercial').value,
        unidade_tributavel: modal.querySelector('#unidade_tributavel').value,
        unidade_tributaria: modal.querySelector('#unidade_tributaria').value,
        tipo_sped: modal.querySelector('#tipo_sped').value,
        codigo_ean: modal.querySelector('#codigo_ean').value,
        codigo_ean_tributavel: modal.querySelector('#codigo_ean_tributavel').value,
        cest: modal.querySelector('#cest').value,
        empresa_id: selectedCompany,
        status: 'novo',
      };

      // Enviar os dados para a API
      fetch('/api/produtos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(formData),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.message) {
            alert(data.message);
            // Fechar o modal
            modalInstance.hide();
            // Recarregar os dados de produtos
            loadProdutoData();
          } else {
            alert('Erro ao criar produto');
          }
        })
        .catch((error) => {
          console.error('Erro ao criar produto:', error);
          alert('Erro ao criar produto');
        });
    });
  }
}
