from flask import Flask, request, jsonify, render_template
from flask_socketio import Socket<PERSON>
from models import db, Escritorio, Empresa, Usuario, Cliente, Produto, Tributo, ImportacaoXML, TributoHistorico
from models.auditoria_status_manual import AuditoriaStatusManual
from routes import cliente_bp, produto_bp, importacao_bp, tributo_bp, cenario_bp, auditoria_bp, system_bp, perfil_bp, relatorio_bp
from routes.dashboard_routes import dashboard_bp
from services.websocket_service import init_websocket_service
from services.queue_manager import init_queue_manager
from services.transaction_manager import init_transaction_manager
import os
from dotenv import load_dotenv
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
import bcrypt
from datetime import datetime, timedelta
import logging

# Carregar variáveis de ambiente
load_dotenv()

def create_app():
    app = Flask(__name__, static_folder='../front/static', template_folder='../front/templates')

    # Configuração do CORS
    CORS(app, resources={
        r"/api/*": {"origins": "*"},
        r"http://cdn.datatables.net/*": {"origins": "*"}
    })

    # Configuração do banco de dados
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'postgresql://postgres:postgres@localhost/auditoria_fiscal')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Configurações de pool de conexões para suporte a múltiplos usuários
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_size': 20,                    # Número de conexões permanentes no pool
        'max_overflow': 30,                 # Conexões extras além do pool_size
        'pool_timeout': 30,                 # Timeout para obter conexão (segundos)
        'pool_recycle': 3600,              # Reciclar conexões a cada 1 hora
        'pool_pre_ping': True,             # Verificar conexões antes de usar
        'connect_args': {
            'connect_timeout': 10,          # Timeout de conexão
            'application_name': 'auditoria_fiscal_app'
        }
    }

    # Configuração do JWT
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'sua_chave_secreta_aqui')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)  # Token válido por 24 horas

    # Configurar logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Inicializar extensões
    db.init_app(app)
    jwt = JWTManager(app)

    # Configurar SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

    # Inicializar serviços de concorrência
    with app.app_context():
        # Inicializar gerenciadores
        queue_manager = init_queue_manager(max_workers=4, max_queue_size=100, app=app)
        transaction_manager = init_transaction_manager()

        # Inicializar serviço WebSocket
        websocket_service = init_websocket_service(socketio)

        logging.info("Serviços de concorrência inicializados")

    # Registrar blueprints
    app.register_blueprint(cliente_bp)
    app.register_blueprint(produto_bp)
    app.register_blueprint(importacao_bp)
    app.register_blueprint(tributo_bp)
    app.register_blueprint(cenario_bp)
    app.register_blueprint(auditoria_bp)
    app.register_blueprint(system_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(perfil_bp)
    app.register_blueprint(relatorio_bp)

    # Rotas para o frontend
    @app.route('/')
    @app.route('/web')
    def index():
        return render_template('index.html')

    @app.route('/dashboard')
    def dashboard():
        return render_template('dashboard.html')

    # Rotas de Auditoria
    @app.route('/auditoria/entrada')
    def auditoria_entrada():
        return render_template('dashboard.html')

    @app.route('/auditoria/saida')
    def auditoria_saida():
        return render_template('dashboard.html')

    # Rotas de detalhes de Auditoria
    @app.route('/auditoria/entrada/<tributo>')
    def auditoria_entrada_tributo(tributo):
        return render_template('dashboard.html')

    @app.route('/auditoria/saida/<tributo>')
    def auditoria_saida_tributo(tributo):
        return render_template('dashboard.html')

    # Rotas de Cenários
    @app.route('/cenarios/entrada')
    def cenarios_entrada():
        return render_template('dashboard.html')

    @app.route('/cenarios/saida')
    def cenarios_saida():
        return render_template('dashboard.html')

    # Rotas de detalhes de Cenários
    @app.route('/cenarios/entrada/<tributo>')
    def cenarios_entrada_tributo(tributo):
        return render_template('dashboard.html')

    @app.route('/cenarios/saida/<tributo>')
    def cenarios_saida_tributo(tributo):
        return render_template('dashboard.html')

    # Outras rotas
    @app.route('/clientes')
    def clientes():
        return render_template('dashboard.html')

    @app.route('/produto')
    def produto():
        return render_template('dashboard.html')

    @app.route('/importacao')
    def importacao():
        return render_template('dashboard.html')

    # Rotas administrativas
    @app.route('/empresas', endpoint='empresas_page')
    def empresas_page():
        return render_template('dashboard.html')

    @app.route('/usuarios', endpoint='usuarios_page')
    def usuarios_page():
        return render_template('dashboard.html')

    @app.route('/escritorios', endpoint='escritorios_page')
    def escritorios_page():
        return render_template('escritorios.html')

    @app.route('/perfil', endpoint='perfil_page')
    def perfil_page():
        return render_template('perfil.html')

    # Rota para dashboard específico da empresa
    @app.route('/dashboard/empresa/<int:empresa_id>')
    def dashboard_empresa(empresa_id):
        return render_template('dashboard.html')

    # API - Autenticação
    @app.route("/login", methods=["POST"])
    def login():
        try:
            data = request.get_json()
            email = data.get("email")
            senha = data.get("senha")

            if not email or not senha:
                return {"message": "Email e senha são obrigatórios"}, 400

            usuario = Usuario.query.filter_by(email=email).first()
            if not usuario:
                return {"message": "Usuário não encontrado"}, 401

            if not bcrypt.checkpw(senha.encode('utf-8'), usuario.senha_hash.encode('utf-8')):
                return {"message": "Senha incorreta"}, 401

            # Criar token com tempo de expiração maior
            access_token = create_access_token(identity=str(usuario.id))

            # Buscar o nome do escritório se o usuário estiver vinculado a um
            escritorio_nome = None
            if usuario.escritorio_id:
                escritorio = Escritorio.query.get(usuario.escritorio_id)
                if escritorio:
                    escritorio_nome = escritorio.nome

            # Retornar mais informações sobre o usuário
            return {
                "access_token": access_token,
                "usuario_id": usuario.id,
                "nome": usuario.nome,
                "email": usuario.email,
                "escritorio_id": usuario.escritorio_id,
                "escritorio_nome": escritorio_nome,
                "is_admin": usuario.is_admin,
                "tipo_usuario": usuario.tipo_usuario,
                "empresas_permitidas": usuario.empresas_permitidas or []
            }
        except Exception as e:
            print(f"Erro ao processar login: {str(e)}")
            return {"message": "Erro ao processar solicitação de login"}, 500

    # API - Informações do usuário logado
    @app.route("/me", methods=["GET"])
    @jwt_required()
    def get_user_info():
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar o nome do escritório se o usuário estiver vinculado a um
            escritorio_nome = None
            if usuario.escritorio_id:
                escritorio = Escritorio.query.get(usuario.escritorio_id)
                if escritorio:
                    escritorio_nome = escritorio.nome

            return {
                "id": usuario.id,
                "nome": usuario.nome,
                "email": usuario.email,
                "escritorio_id": usuario.escritorio_id,
                "escritorio_nome": escritorio_nome,
                "is_admin": usuario.is_admin,
                "tipo_usuario": usuario.tipo_usuario,
                "empresas_permitidas": usuario.empresas_permitidas or []
            }
        except Exception as e:
            print(f"Erro ao buscar informações do usuário: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Escritório
    @app.route("/api/escritorios", methods=["GET"])
    @jwt_required()
    def listar_escritorios():
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Filtrar escritórios com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores veem todos os escritórios
                escritorios = Escritorio.query.all()
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório veem apenas seu próprio escritório
                escritorios = [Escritorio.query.get(usuario.escritorio_id)] if usuario.escritorio_id else []
            else:
                # Usuários comuns veem apenas o escritório ao qual estão vinculados
                escritorios = [Escritorio.query.get(usuario.escritorio_id)] if usuario.escritorio_id else []

            return {"escritorios": [e.to_dict() for e in escritorios if e]}
        except Exception as e:
            print(f"Erro ao listar escritórios: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Escritório - Criar
    @app.route("/api/escritorios", methods=["POST"])
    @jwt_required()
    def criar_escritorio():
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Apenas administradores podem criar escritórios
            if not usuario.is_admin and usuario.tipo_usuario != 'admin':
                return {"message": "Você não tem permissão para criar escritórios"}, 403

            # Validar dados
            nome = data.get("nome")
            cnpj = data.get("cnpj")

            if not nome or not cnpj:
                return {"message": "Nome e CNPJ são obrigatórios"}, 400

            # Verificar se já existe escritório com o mesmo CNPJ
            if Escritorio.query.filter_by(cnpj=cnpj).first():
                return {"message": "Já existe um escritório com este CNPJ"}, 400

            # Criar o escritório
            novo = Escritorio(
                nome=nome,
                cnpj=cnpj,
                endereco=data.get("endereco"),
                email=data.get("email"),
                responsavel=data.get("responsavel")
            )
            db.session.add(novo)
            db.session.commit()

            return {"message": "Escritório criado com sucesso", "id": novo.id}, 201
        except Exception as e:
            print(f"Erro ao criar escritório: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Listar
    @app.route("/api/empresas", methods=["GET"])
    @jwt_required()
    def listar_empresas():
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Filtrar empresas com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores veem todas as empresas
                empresas = Empresa.query.all()
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório veem empresas do seu escritório
                empresas = Empresa.query.filter_by(escritorio_id=usuario.escritorio_id).all()
            else:
                # Usuários comuns veem apenas empresas permitidas
                empresas_permitidas = usuario.empresas_permitidas or []
                empresas = Empresa.query.filter(Empresa.id.in_(empresas_permitidas)).all()

            return {"empresas": [e.to_dict() for e in empresas]}
        except Exception as e:
            print(f"Erro ao listar empresas: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Criar
    @app.route("/api/empresas", methods=["POST"])
    @jwt_required()
    def criar_empresa():
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Determinar o escritório_id com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem especificar o escritório
                escritorio_id = data.get("escritorio_id")
                if not escritorio_id:
                    return {"message": "ID do escritório é obrigatório"}, 400
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório só podem criar empresas para seu próprio escritório
                escritorio_id = usuario.escritorio_id
            else:
                # Usuários comuns não podem criar empresas
                return {"message": "Você não tem permissão para criar empresas"}, 403

            # Validar dados obrigatórios
            razao_social = data.get("razao_social")
            cnpj = data.get("cnpj")

            if not razao_social or not cnpj:
                return {"message": "CNPJ e Razão Social são obrigatórios"}, 400

            # Verificar se já existe empresa com o mesmo CNPJ
            if Empresa.query.filter_by(cnpj=cnpj).first():
                return {"message": "Já existe uma empresa com este CNPJ"}, 400

            # Criar a empresa com todos os campos
            nova = Empresa(
                razao_social=razao_social,
                cnpj=cnpj,
                escritorio_id=escritorio_id,
                inscricao_estadual=data.get("inscricao_estadual"),
                nome_fantasia=data.get("nome_fantasia"),
                email=data.get("email"),
                responsavel=data.get("responsavel"),
                cep=data.get("cep"),
                logradouro=data.get("logradouro"),
                numero=data.get("numero"),
                complemento=data.get("complemento"),
                bairro=data.get("bairro"),
                cidade=data.get("cidade"),
                estado=data.get("estado"),
                cnae=data.get("cnae"),
                tributacao=data.get("tributacao"),
                atividade=data.get("atividade"),
                pis_cofins=data.get("pis_cofins"),
                observacoes=data.get("observacoes")
            )
            db.session.add(nova)
            db.session.commit()

            # Se for um usuário comum, atribuir automaticamente a empresa a ele
            if usuario.tipo_usuario == 'usuario':
                empresas_permitidas = usuario.empresas_permitidas or []
                if nova.id not in empresas_permitidas:
                    empresas_permitidas.append(nova.id)
                    usuario.empresas_permitidas = empresas_permitidas
                    db.session.commit()

            return {"message": "Empresa criada com sucesso", "id": nova.id}, 201
        except Exception as e:
            print(f"Erro ao criar empresa: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Obter
    @app.route("/api/empresas/<int:empresa_id>", methods=["GET"])
    @jwt_required()
    def obter_empresa(empresa_id):
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar a empresa
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                return {"message": "Empresa não encontrada"}, 404

            # Verificar permissões para visualizar a empresa
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem ver qualquer empresa
                pass
            elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem ver empresas do seu próprio escritório
                pass
            elif usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas:
                # Usuários comuns só podem ver empresas permitidas
                pass
            else:
                return {"message": "Você não tem permissão para visualizar esta empresa"}, 403

            return {"empresa": empresa.to_dict()}, 200
        except Exception as e:
            print(f"Erro ao obter empresa: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Atualizar
    @app.route("/api/empresas/<int:empresa_id>", methods=["PUT"])
    @jwt_required()
    def atualizar_empresa(empresa_id):
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar a empresa
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                return {"message": "Empresa não encontrada"}, 404

            # Verificar permissões para editar a empresa
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem editar qualquer empresa
                pass
            elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem editar empresas do seu próprio escritório
                pass
            else:
                # Usuários comuns não podem editar empresas
                return {"message": "Você não tem permissão para editar esta empresa"}, 403

            # Validar dados obrigatórios
            razao_social = data.get("razao_social")
            cnpj = data.get("cnpj")

            if not cnpj or not razao_social:
                return {"message": "CNPJ e Razão Social são obrigatórios"}, 400

            # Verificar se já existe outra empresa com o mesmo CNPJ
            empresa_existente = Empresa.query.filter_by(cnpj=cnpj).first()
            if empresa_existente and empresa_existente.id != empresa_id:
                return {"message": "Já existe outra empresa com este CNPJ"}, 400

            # Atualizar os campos da empresa
            empresa.razao_social = razao_social
            empresa.cnpj = cnpj
            empresa.inscricao_estadual = data.get("inscricao_estadual")
            empresa.nome_fantasia = data.get("nome_fantasia")
            empresa.email = data.get("email")
            empresa.responsavel = data.get("responsavel")
            empresa.cep = data.get("cep")
            empresa.logradouro = data.get("logradouro")
            empresa.numero = data.get("numero")
            empresa.complemento = data.get("complemento")
            empresa.bairro = data.get("bairro")
            empresa.cidade = data.get("cidade")
            empresa.estado = data.get("estado")
            empresa.cnae = data.get("cnae")
            empresa.tributacao = data.get("tributacao")
            empresa.atividade = data.get("atividade")
            empresa.pis_cofins = data.get("pis_cofins")
            empresa.observacoes = data.get("observacoes")

            db.session.commit()

            return {"message": "Empresa atualizada com sucesso"}, 200
        except Exception as e:
            print(f"Erro ao atualizar empresa: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Empresa - Excluir
    @app.route("/api/empresas/<int:empresa_id>", methods=["DELETE"])
    @jwt_required()
    def excluir_empresa(empresa_id):
        try:
            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar a empresa
            empresa = Empresa.query.get(empresa_id)
            if not empresa:
                return {"message": "Empresa não encontrada"}, 404

            # Verificar permissões para excluir a empresa
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem excluir qualquer empresa
                pass
            elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem excluir empresas do seu próprio escritório
                pass
            else:
                # Usuários comuns não podem excluir empresas
                return {"message": "Você não tem permissão para excluir esta empresa"}, 403

            # Excluir a empresa
            db.session.delete(empresa)
            db.session.commit()

            return {"message": "Empresa excluída com sucesso"}, 200
        except Exception as e:
            print(f"Erro ao excluir empresa: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Usuário - Listar
    @app.route("/api/usuarios", methods=["GET"])
    @jwt_required()
    def listar_usuarios():
        try:
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Filtrar usuários com base no tipo de usuário
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores veem todos os usuários
                usuarios = Usuario.query.all()
            elif usuario.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório veem apenas usuários do seu escritório
                usuarios = Usuario.query.filter_by(escritorio_id=usuario.escritorio_id).all()
            else:
                # Usuários comuns veem apenas seu próprio usuário
                usuarios = [usuario]

            return {"usuarios": [u.to_dict() for u in usuarios]}
        except Exception as e:
            print(f"Erro ao listar usuários: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Usuário - Criar
    @app.route("/api/usuarios", methods=["POST"])
    @jwt_required()
    def criar_usuario():
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario_atual = db.session.get(Usuario, usuario_id)

            if not usuario_atual:
                return {"message": "Usuário não encontrado"}, 404

            # Validar dados
            nome = data.get("nome")
            email = data.get("email")
            senha = data.get("senha")

            if not nome or not email or not senha:
                return {"message": "Nome, email e senha são obrigatórios"}, 400

            # Verificar se já existe usuário com o mesmo email
            if Usuario.query.filter_by(email=email).first():
                return {"message": "Já existe um usuário com este email"}, 400

            # Determinar o escritório_id e tipo_usuario com base no tipo de usuário atual
            if usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin':
                # Administradores podem especificar o escritório e tipo de usuário
                escritorio_id = data.get("escritorio_id")
                tipo_usuario = data.get("tipo_usuario", "usuario")
            elif usuario_atual.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório só podem criar usuários para seu próprio escritório
                # e apenas do tipo 'usuario'
                escritorio_id = usuario_atual.escritorio_id
                tipo_usuario = "usuario"
            else:
                # Usuários comuns não podem criar outros usuários
                return {"message": "Você não tem permissão para criar usuários"}, 403

            # Verificar permissões com base no tipo de usuário atual
            if usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin':
                # Administradores podem criar qualquer tipo de usuário
                pass
            elif usuario_atual.tipo_usuario == 'escritorio':
                # Usuários do tipo escritório só podem criar usuários comuns para seu escritório
                if tipo_usuario != 'usuario':
                    return {"message": "Você só pode criar usuários do tipo comum"}, 403

                # Forçar o escritorio_id para o escritório do usuário atual
                if escritorio_id != usuario_atual.escritorio_id:
                    return {"message": "Você só pode criar usuários para o seu próprio escritório"}, 403
            else:
                # Usuários comuns não podem criar outros usuários
                return {"message": "Você não tem permissão para criar usuários"}, 403

            # Gerar hash da senha
            senha_hash = bcrypt.hashpw(senha.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # Se for admin, garantir que is_admin seja True
            is_admin = data.get("is_admin", False)
            if tipo_usuario == "admin":
                is_admin = True

                # Apenas administradores podem criar outros administradores
                if not (usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin'):
                    return {"message": "Você não tem permissão para criar usuários administradores"}, 403

            # Criar o usuário
            novo = Usuario(
                nome=nome,
                email=email,
                senha_hash=senha_hash,
                escritorio_id=escritorio_id,
                is_admin=is_admin,
                tipo_usuario=tipo_usuario,
                empresas_permitidas=data.get("empresas_permitidas", [])
            )
            db.session.add(novo)
            db.session.commit()

            return {"message": "Usuário criado com sucesso", "id": novo.id}, 201
        except Exception as e:
            print(f"Erro ao criar usuário: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Usuário - Obter
    @app.route("/api/usuarios/<int:usuario_id>", methods=["GET"])
    @jwt_required()
    def obter_usuario(usuario_id):
        try:
            usuario_atual_id = get_jwt_identity()
            usuario_atual = db.session.get(Usuario, usuario_atual_id)

            if not usuario_atual:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar o usuário solicitado
            usuario = db.session.get(Usuario, usuario_id)
            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Verificar permissões para visualizar o usuário
            if usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin':
                # Administradores podem ver qualquer usuário
                pass
            elif usuario_atual.tipo_usuario == 'escritorio' and usuario.escritorio_id == usuario_atual.escritorio_id:
                # Usuários do tipo escritório só podem ver usuários do seu próprio escritório
                pass
            elif usuario_atual.id == usuario_id:
                # Usuários podem ver seu próprio perfil
                pass
            else:
                return {"message": "Você não tem permissão para visualizar este usuário"}, 403

            return {"usuario": usuario.to_dict()}, 200
        except Exception as e:
            print(f"Erro ao obter usuário: {str(e)}")
            return {"message": "Erro ao processar solicitação"}, 500

    # CRUD Usuário - Atualizar
    @app.route("/api/usuarios/<int:usuario_id>", methods=["PUT"])
    @jwt_required()
    def atualizar_usuario(usuario_id):
        try:
            data = request.get_json()

            # Verificar permissões
            usuario_atual_id = get_jwt_identity()
            usuario_atual = db.session.get(Usuario, usuario_atual_id)

            if not usuario_atual:
                return {"message": "Usuário não encontrado"}, 404

            # Buscar o usuário a ser atualizado
            usuario = db.session.get(Usuario, usuario_id)
            if not usuario:
                return {"message": "Usuário não encontrado"}, 404

            # Verificar permissões para editar o usuário
            if usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin':
                # Administradores podem editar qualquer usuário
                pass
            elif usuario_atual.tipo_usuario == 'escritorio' and usuario.escritorio_id == usuario_atual.escritorio_id:
                # Usuários do tipo escritório só podem editar usuários do seu próprio escritório
                # E não podem alterar o tipo de usuário para admin ou escritório
                if data.get("tipo_usuario") in ['admin', 'escritorio'] and usuario.tipo_usuario == 'usuario':
                    return {"message": "Você não tem permissão para alterar o tipo deste usuário"}, 403
            elif usuario_atual.id == usuario_id:
                # Usuários podem editar seu próprio perfil, mas não podem alterar o tipo
                if data.get("tipo_usuario") != usuario.tipo_usuario:
                    return {"message": "Você não tem permissão para alterar seu tipo de usuário"}, 403
            else:
                return {"message": "Você não tem permissão para editar este usuário"}, 403

            # Validar dados
            nome = data.get("nome")
            email = data.get("email")

            if not nome or not email:
                return {"message": "Nome e email são obrigatórios"}, 400

            # Verificar se já existe outro usuário com o mesmo email
            usuario_existente = Usuario.query.filter_by(email=email).first()
            if usuario_existente and usuario_existente.id != usuario_id:
                return {"message": "Já existe outro usuário com este email"}, 400

            # Atualizar os campos do usuário
            usuario.nome = nome
            usuario.email = email

            # Atualizar senha se fornecida
            senha = data.get("senha")
            if senha:
                usuario.senha_hash = bcrypt.hashpw(senha.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # Atualizar tipo de usuário se permitido
            if (usuario_atual.is_admin or usuario_atual.tipo_usuario == 'admin') and data.get("tipo_usuario"):
                usuario.tipo_usuario = data.get("tipo_usuario")
                # Se for admin, garantir que is_admin seja True
                if data.get("tipo_usuario") == "admin":
                    usuario.is_admin = True
                else:
                    usuario.is_admin = data.get("is_admin", False)

            # Atualizar empresas permitidas
            if data.get("empresas_permitidas") is not None:
                usuario.empresas_permitidas = data.get("empresas_permitidas")

            db.session.commit()

            return {"message": "Usuário atualizado com sucesso"}, 200
        except Exception as e:
            print(f"Erro ao atualizar usuário: {str(e)}")
            db.session.rollback()
            return {"message": "Erro ao processar solicitação"}, 500

    # Eventos WebSocket
    @socketio.on('connect')
    def handle_connect(auth):
        """
        Evento de conexão WebSocket
        """
        print(f"Cliente conectado: {request.sid}")

    @socketio.on('disconnect')
    def handle_disconnect():
        """
        Evento de desconexão WebSocket
        """
        print(f"Cliente desconectado: {request.sid}")

    @socketio.on('join_import')
    def handle_join_import(data):
        """
        Evento para entrar na sala de uma importação
        """
        try:
            token = data.get('token')
            import_id = data.get('import_id')

            if not token or not import_id:
                return {'error': 'Token e import_id são obrigatórios'}

            user_id = websocket_service.authenticate_user(token)
            if not user_id:
                return {'error': 'Token inválido'}

            room = websocket_service.join_import_room(user_id, import_id)
            return {'success': True, 'room': room}

        except Exception as e:
            print(f"Erro ao entrar na sala de importação: {str(e)}")
            return {'error': 'Erro interno do servidor'}

    @socketio.on('join_audit')
    def handle_join_audit(data):
        """
        Evento para entrar na sala de uma auditoria
        """
        try:
            token = data.get('token')
            audit_id = data.get('audit_id')

            if not token or not audit_id:
                return {'error': 'Token e audit_id são obrigatórios'}

            user_id = websocket_service.authenticate_user(token)
            if not user_id:
                return {'error': 'Token inválido'}

            room = websocket_service.join_audit_room(user_id, audit_id)
            return {'success': True, 'room': room}

        except Exception as e:
            print(f"Erro ao entrar na sala de auditoria: {str(e)}")
            return {'error': 'Erro interno do servidor'}

    return app, socketio

if __name__ == '__main__':
    app, socketio = create_app()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
