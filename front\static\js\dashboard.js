/**
 * Dashboard.js - Auditoria Fiscal
 * Funções para o dashboard principal e navegação
 */

// Variáveis globais
let currentUser = null;
let selectedCompany = null;
let selectedYear = new Date().getFullYear();
let selectedMonth = new Date().getMonth() + 1; // 1-12
let darkThemeEnabled = false;
let isInitialized = false; // Flag para controlar inicialização única

// Inicialização
document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM carregado - dashboard.js');
  // Verificar autenticação
  checkAuth();
});

/**
 * Verifica se o usuário está autenticado
 */
function checkAuth() {
  const token = localStorage.getItem('token');

  if (!token) {
    console.error('Token não encontrado, redirecionando para login');
    window.location.href = '/web';
    return;
  }

  // Verificar se o token é válido fazendo uma requisição para /me
  fetch('/me', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => {
      if (response.status === 401) {
        console.error('Token inválido, redirecionando para login');
        localStorage.removeItem('token');
        window.location.href = '/web';
        return;
      }

      // Token válido, continuar com a inicialização
      return response.json();
    })
    .then((user) => {
      if (user) {
        // Carregar informações do usuário
        loadUserInfo(user);

        // Continuar com a inicialização do dashboard
        initDashboard();
      }
    })
    .catch((error) => {
      console.error('Erro ao verificar token:', error);
      // Mesmo com erro, tentar continuar com a sessão
      initDashboard();
    });
}

/**
 * Carrega informações do usuário
 * @param {Object} user - Dados do usuário (opcional)
 */
function loadUserInfo(user = null) {
  if (!user) {
    // Tentar carregar do localStorage
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        currentUser = JSON.parse(storedUser);
        console.log('Usuário carregado do localStorage:', currentUser);
        // Carregar empresas imediatamente após carregar usuário do localStorage
        if (currentUser.tipo_usuario === 'admin' || currentUser.is_admin) {
          loadAllCompanies();
        } else if (currentUser.tipo_usuario === 'escritorio') {
          loadEscritorioCompanies(currentUser.escritorio_id);
        } else if (
          currentUser.empresas_permitidas &&
          currentUser.empresas_permitidas.length > 0
        ) {
          loadUserCompanies(currentUser.empresas_permitidas);
        }
      } catch (e) {
        console.error('Erro ao carregar dados do usuário:', e);
        currentUser = null;
      }
    }

    // Se ainda não tiver dados, fazer requisição
    if (!currentUser) {
      fetch('/me', {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      })
        .then((response) => response.json())
        .then((userData) => {
          loadUserInfo(userData);
        })
        .catch((error) => {
          console.error('Erro ao carregar dados do usuário:', error);
        });
      return;
    }
  } else {
    // Armazenar dados do usuário globalmente e no localStorage
    currentUser = user;
    localStorage.setItem('currentUser', JSON.stringify(user));
  }

  // Exibir nome do usuário
  document.getElementById('user-name').textContent =
    currentUser.nome || 'Usuário';

  // Carregar empresas para o seletor
  if (currentUser.tipo_usuario === 'admin' || currentUser.is_admin) {
    // Administradores veem todas as empresas
    loadAllCompanies();
  } else if (currentUser.tipo_usuario === 'escritorio') {
    // Usuários do tipo escritório veem empresas do seu escritório
    loadEscritorioCompanies(currentUser.escritorio_id);
  } else if (
    currentUser.empresas_permitidas &&
    currentUser.empresas_permitidas.length > 0
  ) {
    // Usuários comuns veem apenas empresas permitidas
    loadUserCompanies(currentUser.empresas_permitidas);
  }
}

/**
 * Inicializa o dashboard
 */
function initDashboard() {
  // Verificar se já foi inicializado para evitar duplicação de event listeners
  if (isInitialized) {
    console.log('Dashboard já inicializado, atualizando apenas os dados');
    // Atualizar apenas os dados
    showCurrentPage();
    loadPageData();
    return;
  }

  console.log('Inicializando dashboard pela primeira vez');
  isInitialized = true;

  // Configurar componentes na ordem correta
  setupCompanySelector();
  loadThemePreference();
  setupThemeToggle();
  setupSidebarHoverEvents();
  setupSidebarNavigation();
  setupProfileDropdownLinks();
  setupSidebarToggle();
  setupLogoutButton();

  // Se já temos um usuário, carregar empresas
  if (currentUser) {
    if (currentUser.tipo_usuario === 'admin' || currentUser.is_admin) {
      loadAllCompanies();
    } else if (currentUser.tipo_usuario === 'escritorio') {
      loadEscritorioCompanies(currentUser.escritorio_id);
    } else if (
      currentUser.empresas_permitidas &&
      currentUser.empresas_permitidas.length > 0
    ) {
      loadUserCompanies(currentUser.empresas_permitidas);
    }
  }

  // Configurar seletor de empresa
  setupCompanySelector();

  // Configurar seletor de ano
  setupYearSelector();

  // Configurar seletor de mês
  setupMonthSelector();

  // Configurar botão de alternância de tema
  setupThemeToggle();

  // Configurar botão de logout
  setupLogoutButton();

  // Mostrar a página correta com base na URL atual
  showCurrentPage();

  // Carregar dados iniciais
  loadPageData();
}

/**
 * Carrega a preferência de tema do usuário
 */
function loadThemePreference() {
  const savedTheme = localStorage.getItem('darkTheme');

  if (savedTheme === 'true') {
    darkThemeEnabled = true;
    document.body.classList.add('dark-theme');

    // Atualizar ícone do botão se ele já existir
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    if (themeToggleBtn) {
      const icon = themeToggleBtn.querySelector('i');
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
    }
  }
}

/**
 * Configura o botão de alternância de tema
 */
function setupThemeToggle() {
  const themeToggleBtn = document.getElementById('theme-toggle-btn');

  if (themeToggleBtn) {
    // Remover event listeners anteriores (se possível)
    const newThemeToggleBtn = themeToggleBtn.cloneNode(true);
    themeToggleBtn.parentNode.replaceChild(newThemeToggleBtn, themeToggleBtn);

    // Atualizar ícone inicial com base no tema atual
    const icon = newThemeToggleBtn.querySelector('i');
    if (darkThemeEnabled) {
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
    }

    newThemeToggleBtn.addEventListener('click', function () {
      // Alternar o tema
      darkThemeEnabled = !darkThemeEnabled;

      // Salvar preferência
      localStorage.setItem('darkTheme', darkThemeEnabled);

      // Aplicar ou remover classe do body com uma pequena animação
      if (darkThemeEnabled) {
        document.body.classList.add('dark-theme');
        // Animar o ícone
        icon.style.transform = 'rotate(360deg)';
        setTimeout(() => {
          icon.classList.remove('fa-moon');
          icon.classList.add('fa-sun');
          icon.style.transform = '';
        }, 150);
      } else {
        document.body.classList.remove('dark-theme');
        // Animar o ícone
        icon.style.transform = 'rotate(-360deg)';
        setTimeout(() => {
          icon.classList.remove('fa-sun');
          icon.classList.add('fa-moon');
          icon.style.transform = '';
        }, 150);
      }
    });
  }
}

/**
 * Configura eventos de hover para os dropdowns na sidebar recolhida
 */
function setupSidebarHoverEvents() {
  // Esta função foi movida para dentro de setupSidebarToggle
  // para evitar duplicação de event listeners
}

/**
 * Mostra a página correta com base na URL atual
 */
function showCurrentPage() {
  const currentPath = window.location.pathname;
  let currentPage = 'dashboard'; // Padrão

  console.log('showCurrentPage - URL atual:', currentPath);

  // Verificar se estamos em uma página de detalhes de cenários
  const parts = currentPath.split('/');
  const isCenarioDetalhe =
    parts.length >= 4 &&
    parts[1] === 'cenarios' &&
    (parts[2] === 'entrada' || parts[2] === 'saida') &&
    ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins'].includes(parts[3]);

  // Verificar se estamos em uma página de auditoria específica
  const isAuditoriaDetalhe =
    parts.length >= 4 &&
    parts[1] === 'auditoria' &&
    (parts[2] === 'entrada' || parts[2] === 'saida') &&
    ['icms', 'icms-st', 'difal', 'ipi', 'pis', 'cofins'].includes(parts[3]);

  if (isCenarioDetalhe) {
    console.log('Detectada página de detalhes de cenário:', parts[3]);
    // Não fazer nada aqui, deixar o cenarios_detalhes.js lidar com isso
    return;
  }

  if (isAuditoriaDetalhe) {
    console.log('Detectada página de auditoria específica:', parts[3]);
    // Mostrar a página de auditoria específica
    const pages = document.querySelectorAll('.page-section');
    pages.forEach((page) => {
      page.classList.remove('active');
    });

    const auditoriaPage = document.getElementById('page-auditoria-tributo');
    if (auditoriaPage) {
      auditoriaPage.classList.add('active');

      // Atualizar o título da página
      const tituloElement = document.getElementById('auditoria-tributo-titulo');
      if (tituloElement) {
        const tipoOperacao = parts[2] === 'entrada' ? 'Entrada' : 'Saída';
        const tipoTributo = parts[3].toUpperCase().replace('-', '-');
        tituloElement.textContent = `Auditoria de ${tipoTributo} - ${tipoOperacao}`;
      }

      // Configurar o botão de executar auditoria
      const btnExecutarAuditoria = document.getElementById(
        'btn-executar-auditoria',
      );
      if (btnExecutarAuditoria) {
        btnExecutarAuditoria.onclick = function () {
          executarAuditoriaTributo(parts[3], parts[2]);
        };
      }
    }
    return;
  }

  // Determinar a página atual com base na URL
  if (currentPath.includes('/auditoria/entrada')) {
    currentPage = 'auditoria-entrada';
  } else if (currentPath.includes('/auditoria/saida')) {
    currentPage = 'auditoria-saida';
  } else if (currentPath.includes('/cenarios/entrada')) {
    currentPage = 'cenarios-entrada';
  } else if (currentPath.includes('/cenarios/saida')) {
    currentPage = 'cenarios-saida';
  } else if (currentPath.includes('/clientes')) {
    currentPage = 'clientes';
  } else if (currentPath.includes('/produto')) {
    currentPage = 'produto';
  } else if (currentPath.includes('/importacao')) {
    currentPage = 'importacao';
  } else if (currentPath.includes('/empresas')) {
    currentPage = 'empresas';
  } else if (currentPath.includes('/usuarios')) {
    currentPage = 'usuarios';
  } else if (currentPath.includes('/escritorios')) {
    currentPage = 'escritorios';
  }

  console.log('Página atual identificada:', currentPage);

  // Esconder todas as páginas
  const pages = document.querySelectorAll('.page-section');
  pages.forEach((page) => {
    page.classList.remove('active');
  });

  // Mostrar a página atual
  const currentPageElement = document.getElementById(`page-${currentPage}`);
  if (currentPageElement) {
    currentPageElement.classList.add('active');
    console.log('Ativada página:', `page-${currentPage}`);
  } else {
    // Se a página não existir, mostrar a página de dashboard como fallback
    const dashboardPage = document.getElementById('page-dashboard');
    if (dashboardPage) {
      dashboardPage.classList.add('active');
      console.log('Fallback para dashboard');
    }
  }
}

/**
 * Carrega os dados específicos da página atual
 */
function loadPageData() {
  const currentPath = window.location.pathname;
  console.log('loadPageData - URL atual:', currentPath);

  // Verificar se estamos em uma página de detalhes de cenários
  const parts = currentPath.split('/');
  const isCenarioDetalhe =
    parts.length >= 4 &&
    parts[1] === 'cenarios' &&
    (parts[2] === 'entrada' || parts[2] === 'saida') &&
    ['icms', 'icms_st', 'difal', 'ipi', 'pis', 'cofins'].includes(parts[3]);

  // Verificar se estamos em uma página de auditoria específica
  const isAuditoriaDetalhe =
    parts.length >= 4 &&
    parts[1] === 'auditoria' &&
    (parts[2] === 'entrada' || parts[2] === 'saida') &&
    ['icms', 'icms-st', 'difal', 'ipi', 'pis', 'cofins'].includes(parts[3]);

  if (isCenarioDetalhe) {
    console.log('Carregando página de detalhes de cenário:', parts[3]);

    // Verificar se o script cenarios_detalhes.js está carregado
    if (typeof window.cenariosDetalhes !== 'undefined') {
      console.log(
        'Namespace cenariosDetalhes encontrado, chamando setupCenariosDetalhesPage()',
      );
      setupCenariosDetalhesPage();
    } else {
      console.error(
        'Namespace cenariosDetalhes não encontrado. Tentando carregar o script...',
      );

      // Tentar carregar o script dinamicamente
      const script = document.createElement('script');
      script.src = '/static/js/cenarios_detalhes.js';
      script.onload = function () {
        console.log('Script cenarios_detalhes.js carregado com sucesso!');
        if (typeof setupCenariosDetalhesPage === 'function') {
          setupCenariosDetalhesPage();
        }
      };
      script.onerror = function () {
        console.error('Erro ao carregar o script cenarios_detalhes.js');
      };
      document.head.appendChild(script);
    }
    return;
  }

  if (isAuditoriaDetalhe) {
    console.log('Carregando página de auditoria específica:', parts[3]);
    // Carregar o dashboard de auditoria
    carregarDashboardAuditoria(parts[3]);
    return;
  }

  if (currentPath.includes('/auditoria/entrada')) {
    loadAuditoriaEntradaData();
  } else if (currentPath.includes('/auditoria/saida')) {
    loadAuditoriaSaidaData();
  } else if (currentPath.includes('/cenarios/entrada')) {
    loadCenariosEntradaData();
  } else if (currentPath.includes('/cenarios/saida')) {
    loadCenariosSaidaData();
  } else if (currentPath.includes('/clientes')) {
    loadClientesData();
  } else if (currentPath.includes('/produto')) {
    loadProdutoData();
  } else if (currentPath.includes('/importacao')) {
    loadImportacaoData();
  } else if (currentPath.includes('/empresas')) {
    loadEmpresasData();
  } else if (currentPath.includes('/usuarios')) {
    loadUsuariosData();
  } else if (currentPath.includes('/escritorios')) {
    loadEscritoriosData();
  } else {
    // Dashboard é a página padrão
    loadDashboardData();
  }
}

/**
 * Configura a navegação da sidebar
 */
function setupSidebarNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  const dropdownItems = document.querySelectorAll('.nav-dropdown-item');
  const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

  // Marcar o item ativo com base na URL atual
  const currentPath = window.location.pathname;
  let currentPage = 'home'; // Padrão
  let parentPage = '';

  // Determinar a página atual com base na URL
  if (currentPath.includes('/auditoria/entrada')) {
    currentPage = 'auditoria-entrada';
    parentPage = 'auditoria';
  } else if (currentPath.includes('/auditoria/saida')) {
    currentPage = 'auditoria-saida';
    parentPage = 'auditoria';
  } else if (currentPath.includes('/cenarios/entrada')) {
    currentPage = 'cenarios-entrada';
    parentPage = 'cenarios';
  } else if (currentPath.includes('/cenarios/saida')) {
    currentPage = 'cenarios-saida';
    parentPage = 'cenarios';
  } else if (currentPath.includes('/clientes')) {
    currentPage = 'clientes';
  } else if (currentPath.includes('/produto')) {
    currentPage = 'produto';
  } else if (currentPath.includes('/importacao')) {
    currentPage = 'importacao';
  } else if (currentPath.includes('/empresas')) {
    currentPage = 'empresas';
  } else if (currentPath.includes('/usuarios')) {
    currentPage = 'usuarios';
  } else if (currentPath.includes('/escritorios')) {
    currentPage = 'escritorios';
  }

  // Remover classe active de todos os itens
  navItems.forEach((item) => {
    item.classList.remove('active');

    // Adicionar classe active ao item correspondente à página atual ou ao seu pai
    if (item.dataset.page === currentPage || item.dataset.page === parentPage) {
      item.classList.add('active');

      // Se for um dropdown, abri-lo apenas se a sidebar não estiver colapsada
      if (item.classList.contains('nav-dropdown')) {
        const container = document.querySelector('.dashboard-container');
        // Só abrir o dropdown se a sidebar NÃO estiver colapsada
        if (container && !container.classList.contains('sidebar-collapsed')) {
          item.classList.add('open');
        } else {
          // Se a sidebar estiver colapsada, garantir que o dropdown esteja fechado
          item.classList.remove('open');
        }
      }
    }
  });

  // Marcar o item do dropdown ativo
  dropdownItems.forEach((item) => {
    item.classList.remove('active');

    if (item.dataset.page === currentPage) {
      item.classList.add('active');
    }
  });

  // Configurar os toggles dos dropdowns
  document.querySelectorAll('.nav-dropdown > a').forEach((toggle) => {
    // Remover event listeners anteriores (se possível)
    const newToggle = toggle.cloneNode(true);
    toggle.parentNode.replaceChild(newToggle, toggle);

    newToggle.addEventListener('click', function (e) {
      e.preventDefault();
      const parent = this.closest('.nav-dropdown');

      // Fechar outros dropdowns
      document.querySelectorAll('.nav-dropdown').forEach((dropdown) => {
        if (dropdown !== parent) {
          dropdown.classList.remove('open');
        }
      });

      // Alternar o estado do dropdown atual
      parent.classList.toggle('open');
    });
  });

  // Configurar os links do dropdown do perfil
  setupProfileDropdownLinks();
}

/**
 * Configura os links do dropdown do perfil do usuário
 */
function setupProfileDropdownLinks() {
  const empresasLink = document.getElementById('menu-empresas');
  const usuariosLink = document.getElementById('menu-usuarios');
  const escritoriosLink = document.getElementById('menu-escritorios');

  // Função auxiliar para clonar e substituir um elemento para remover event listeners
  function resetElement(element) {
    if (!element) return null;
    const newElement = element.cloneNode(true);
    element.parentNode.replaceChild(newElement, element);
    return newElement;
  }

  // Resetar e configurar link de empresas
  if (empresasLink) {
    const newEmpresasLink = resetElement(empresasLink);
    newEmpresasLink.addEventListener('click', function (e) {
      e.preventDefault();
      window.location.href = '/empresas';
    });
  }

  // Resetar e configurar link de usuários
  if (usuariosLink) {
    const newUsuariosLink = resetElement(usuariosLink);
    newUsuariosLink.addEventListener('click', function (e) {
      e.preventDefault();
      window.location.href = '/usuarios';
    });
  }

  // Resetar e configurar link de escritórios
  if (escritoriosLink) {
    const newEscritoriosLink = resetElement(escritoriosLink);

    // Mostrar o link de escritórios apenas para usuários admin
    if (
      currentUser &&
      (currentUser.is_admin || currentUser.tipo_usuario === 'admin')
    ) {
      newEscritoriosLink.style.display = 'block';

      newEscritoriosLink.addEventListener('click', function (e) {
        e.preventDefault();
        window.location.href = '/escritorios';
      });
    } else {
      newEscritoriosLink.style.display = 'none';
    }
  }
}

/**
 * Configura o toggle da sidebar
 */
function setupSidebarToggle() {
  const toggleBtn = document.getElementById('toggle-sidebar');
  // Remover event listeners anteriores (se possível)
  const newToggleBtn = toggleBtn.cloneNode(true);
  toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);

  const container = document.querySelector('.dashboard-container');
  const dropdowns = document.querySelectorAll('.nav-dropdown');

  // Armazenar referências às funções de handler para poder removê-las depois
  const mouseEnterHandlers = new Map();
  const mouseLeaveHandlers = new Map();

  // Função para adicionar event listeners de hover
  function addHoverListeners() {
    dropdowns.forEach((dropdown, index) => {
      const dropdownMenu = dropdown.querySelector('.nav-dropdown-menu');
      const dropdownToggle = dropdown.querySelector('.nav-link');

      // Criar funções de handler e armazená-las para remoção posterior
      const mouseEnterHandler = function () {
        if (container.classList.contains('sidebar-collapsed')) {
          // Posicionar o menu dropdown ao lado do item
          const rect = dropdownToggle.getBoundingClientRect();
          dropdownMenu.style.top = rect.top + 'px';
          dropdown.classList.add('open');
        }
      };

      const mouseLeaveHandler = function (event) {
        if (container.classList.contains('sidebar-collapsed')) {
          // Verificar se o mouse está saindo para o dropdown
          const relatedTarget = event.relatedTarget;
          if (!dropdown.contains(relatedTarget)) {
            // Adicionar um pequeno delay para permitir que o usuário mova o mouse para o dropdown
            setTimeout(() => {
              if (!dropdown.matches(':hover')) {
                dropdown.classList.remove('open');
              }
            }, 100);
          }
        }
      };

      // Armazenar referências
      mouseEnterHandlers.set(dropdown, mouseEnterHandler);
      mouseLeaveHandlers.set(dropdown, mouseLeaveHandler);

      // Adicionar event listeners
      dropdown.addEventListener('mouseenter', mouseEnterHandler);
      dropdown.addEventListener('mouseleave', mouseLeaveHandler);
    });
  }

  // Função para remover event listeners de hover
  function removeHoverListeners() {
    dropdowns.forEach((dropdown) => {
      const mouseEnterHandler = mouseEnterHandlers.get(dropdown);
      const mouseLeaveHandler = mouseLeaveHandlers.get(dropdown);

      if (mouseEnterHandler) {
        dropdown.removeEventListener('mouseenter', mouseEnterHandler);
      }

      if (mouseLeaveHandler) {
        dropdown.removeEventListener('mouseleave', mouseLeaveHandler);
      }
    });

    // Limpar os maps
    mouseEnterHandlers.clear();
    mouseLeaveHandlers.clear();
  }

  // Configurar estado inicial com base no localStorage
  const storedSidebarState = localStorage.getItem('sidebarCollapsed');

  if (storedSidebarState === 'true') {
    container.classList.add('sidebar-collapsed');
    // Fechar todos os dropdowns quando a sidebar estiver colapsada
    dropdowns.forEach((dropdown) => {
      dropdown.classList.remove('open');
    });
    addHoverListeners();
  } else if (storedSidebarState === 'false') {
    container.classList.remove('sidebar-collapsed');
    removeHoverListeners();
  } else if (container.classList.contains('sidebar-collapsed')) {
    // Fechar todos os dropdowns quando a sidebar estiver colapsada
    dropdowns.forEach((dropdown) => {
      dropdown.classList.remove('open');
    });
    addHoverListeners();
  }

  newToggleBtn.addEventListener('click', function () {
    // Fechar todos os dropdowns antes de colapsar/expandir a sidebar
    dropdowns.forEach((dropdown) => {
      dropdown.classList.remove('open');
    });

    // Toggle da classe sidebar-collapsed
    container.classList.toggle('sidebar-collapsed');

    // Salvar estado no localStorage
    const isCollapsed = container.classList.contains('sidebar-collapsed');
    localStorage.setItem('sidebarCollapsed', isCollapsed);

    // Adicionar ou remover event listeners com base no estado da sidebar
    if (isCollapsed) {
      addHoverListeners();
    } else {
      removeHoverListeners();
    }
  });
}

/**
 * Configura o seletor de empresa
 */
function setupCompanySelector() {
  // Configurar o seletor de empresa
  const companySelect = document.getElementById('company-select');

  if (!companySelect) {
    console.error('Elemento company-select não encontrado');
    return;
  }

  // Carregar empresa salva do localStorage
  const empresaSalva = localStorage.getItem('selectedCompany');
  if (empresaSalva) {
    selectedCompany = empresaSalva;
  }

  // Remover event listeners anteriores (se possível)
  const newCompanySelect = companySelect.cloneNode(true);
  companySelect.parentNode.replaceChild(newCompanySelect, companySelect);

  // Configurar evento de mudança
  newCompanySelect.addEventListener('change', function () {
    const novaEmpresa = this.value;
    selectedCompany = novaEmpresa;
    localStorage.setItem('selectedCompany', novaEmpresa);
    window.selectedCompany = novaEmpresa;

    // Disparar um evento personalizado para notificar outros componentes
    const event = new CustomEvent('company-changed', {
      detail: { companyId: novaEmpresa },
    });
    window.dispatchEvent(event);
    console.log('Evento company-changed disparado com ID:', novaEmpresa);

    // Recarregar dados da página atual
    loadPageData();
  });
}

/**
 * Configura o seletor de ano
 */
function setupYearSelector() {
  const yearSelect = document.getElementById('year-select');

  // Remover event listeners anteriores (se possível)
  const newYearSelect = yearSelect.cloneNode(false); // false para não clonar as opções
  yearSelect.parentNode.replaceChild(newYearSelect, yearSelect);

  // Preencher opções de ano (atual e 5 anos anteriores)
  const currentYear = new Date().getFullYear();
  for (let year = currentYear; year >= currentYear - 5; year--) {
    const option = document.createElement('option');
    option.value = year;
    option.textContent = year;
    newYearSelect.appendChild(option);
  }

  // Configurar evento de mudança
  newYearSelect.addEventListener('change', function () {
    selectedYear = parseInt(this.value);

    // Recarregar dados com o novo ano selecionado
    const activeSection = document.querySelector('.content-section.active');
    if (activeSection) {
      const sectionId = activeSection.id.replace('sec-', '');
      showSection(sectionId);
    }
  });
}

/**
 * Carrega todas as empresas para o seletor
 */
function loadAllCompanies() {
  console.log('Iniciando carregamento de todas as empresas...');
  fetch('/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      console.log('Resposta da API empresas:', response.status);
      return response.json();
    })
    .then((data) => {
      console.log('Dados recebidos da API empresas:', data);
      if (!data.empresas) {
        console.error('Dados de empresas inválidos:', data);
        return;
      }
      populateCompanySelector(data.empresas);
    })
    .catch((error) => {
      console.error('Erro ao carregar empresas:', error);
    });
}

/**
 * Carrega empresas do escritório para o seletor
 * @param {number} escritorioId - ID do escritório
 */
function loadEscritorioCompanies(escritorioId) {
  fetch('/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Filtrar empresas do escritório (já deve vir filtrado do backend)
      populateCompanySelector(data.empresas);
    })
    .catch((error) => {
      console.error('Erro ao carregar empresas do escritório:', error);
    });
}

/**
 * Carrega empresas permitidas para o usuário
 * @param {Array} empresasPermitidas - Array de IDs de empresas permitidas
 */
function loadUserCompanies(empresasPermitidas) {
  fetch('/api/empresas', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Filtrar empresas permitidas (já deve vir filtrado do backend)
      populateCompanySelector(data.empresas);
    })
    .catch((error) => {
      console.error('Erro ao carregar empresas do usuário:', error);
    });
}

/**
 * Preenche o seletor de empresas com as opções
 * @param {Array} empresas - Lista de empresas
 */
function populateCompanySelector(empresas) {
  // Verificar se temos empresas válidas
  if (!Array.isArray(empresas)) {
    console.error('Lista de empresas inválida:', empresas);
    return;
  }

  // Obter o seletor atual (que pode ter sido substituído)
  const companySelect = document.getElementById('company-select');

  if (!companySelect) {
    console.error('Elemento company-select não encontrado no DOM');
    return;
  }

  // Verificar se já existe uma empresa selecionada
  const empresaSalva = localStorage.getItem('selectedCompany');

  // Limpar opções existentes, mantendo a primeira
  while (companySelect.options.length > 1) {
    companySelect.remove(1);
  }

  // Adicionar novas opções
  empresas.forEach((empresa) => {
    const option = document.createElement('option');
    option.value = empresa.id;
    option.textContent =
      empresa.razao_social || empresa.nome_fantasia || empresa.nome;
    companySelect.appendChild(option);
  });

  // Selecionar empresa
  if (
    empresaSalva &&
    empresas.some((e) => e.id.toString() === empresaSalva.toString())
  ) {
    selectedCompany = empresaSalva;
    companySelect.value = empresaSalva;
  } else if (empresas.length > 0) {
    // Selecionar primeira empresa se nenhuma estiver selecionada
    selectedCompany = empresas[0].id;
    companySelect.value = selectedCompany;
    localStorage.setItem('selectedCompany', selectedCompany);
  }

  // Disparar evento de mudança para notificar outros componentes
  companySelect.dispatchEvent(new Event('change'));

  // Atualizar variável global
  window.selectedCompany = selectedCompany;
}

/**
 * Carrega dados para o dashboard principal
 */
function loadDashboardData() {
  // Implementar carregamento de dados do dashboard
  console.log('Carregando dados do dashboard...');

  // Verificar se estamos na página do dashboard
  if (!window.location.pathname.endsWith('/dashboard')) {
    console.log(
      'Não estamos na página do dashboard, ignorando carregamento de contadores',
    );
    return;
  }

  // Exemplo: atualizar contadores
  const contadores = {
    'total-documentos': '0',
    'total-conformes': '0',
    'total-pendentes': '0',
    'total-nao-conformes': '0',
  };

  // Atualizar apenas os contadores que existem
  Object.entries(contadores).forEach(([id, valor]) => {
    const elemento = document.getElementById(id);
    if (elemento) {
      elemento.textContent = valor;
    } else {
      console.log(`Elemento ${id} não encontrado no DOM`);
    }
  });

  // Aqui você faria uma requisição para obter os dados reais
  // e atualizar os elementos da interface
}

/**
 * Carrega dados para a seção de documentos fiscais
 */
function loadDocumentosData() {
  // Implementar carregamento de dados de documentos
  console.log('Carregando dados de documentos fiscais...');

  // Aqui você faria uma requisição para obter os documentos
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a seção de empresas
 */
function loadEmpresasData() {
  // Implementar carregamento de dados de empresas
  console.log('Carregando dados de empresas...');

  // Aqui você faria uma requisição para obter as empresas
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a página de auditoria de entrada
 */
function loadAuditoriaEntradaData() {
  // Implementar carregamento de dados de auditoria de entrada
  console.log('Carregando dados de auditoria de entrada...');

  // Aqui você faria uma requisição para obter os dados
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a página de auditoria de saída
 */
function loadAuditoriaSaidaData() {
  // Implementar carregamento de dados de auditoria de saída
  console.log('Carregando dados de auditoria de saída...');

  // Aqui você faria uma requisição para obter os dados
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a página de cenários de entrada
 */
function loadCenariosEntradaData() {
  // Implementar carregamento de dados de cenários de entrada
  console.log('Carregando dados de cenários de entrada...');

  // Aqui você faria uma requisição para obter os dados
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a página de cenários de saída
 */
function loadCenariosSaidaData() {
  // Implementar carregamento de dados de cenários de saída
  // Aqui você faria uma requisição para obter os dados
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a página de clientes
 */
function loadClientesData() {
  // Implementar carregamento de dados de clientes
  console.log('Carregando dados de clientes...');

  // Aqui você faria uma requisição para obter os dados
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a página de produto
 */
function loadProdutoData() {
  // Implementar carregamento de dados de produto
  console.log('Carregando dados de produto...');

  // Aqui você faria uma requisição para obter os dados
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a página de importação
 */
function loadImportacaoData() {
  // Implementar carregamento de dados de importação
  console.log('Carregando dados de importação...');

  // Aqui você faria uma requisição para obter os dados
  // e atualizar a tabela ou lista
}

/**
 * Carrega dados para a página de escritórios
 */
function loadEscritoriosData() {
  // Verificar se já estamos na página de escritórios para evitar loop
  if (window.location.pathname !== '/escritorios') {
    // Redirecionar para a página de escritórios
    window.location.href = '/escritorios';
  }
}

/**
 * Carrega dados para a página de usuários
 */
function loadUsuariosData() {
  // Implementar carregamento de dados de usuários
  console.log('Carregando dados de usuários...');

  // Aqui você faria uma requisição para obter os usuários
  // e atualizar a tabela ou lista
}

/**
 * Configura o botão de logout
 */
function setupLogoutButton() {
  const logoutBtn = document.getElementById('logout-btn');

  logoutBtn.addEventListener('click', function (e) {
    e.preventDefault();
    performLogout();
  });
}

/**
 * Realiza o logout do usuário
 */
function performLogout() {
  // Remover token e dados salvos
  localStorage.removeItem('token');
  localStorage.removeItem('selectedCompany');
  localStorage.removeItem('currentUser');

  // Redirecionar para a página de login
  window.location.href = '/web';
}
