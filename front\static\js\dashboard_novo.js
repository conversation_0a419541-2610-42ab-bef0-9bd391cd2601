/**
 * Dashboard Novo - Auditoria Fiscal
 * Novo dashboard reformulado com hierarquia de usuários
 */

// Variáveis globais
let dashboardData = null;
let empresasData = null;

/**
 * Inicializa o novo dashboard
 */
function initNovoDashboard() {
  console.log('Inicializando novo dashboard...');

  // Verificar se estamos na página correta
  if (
    !window.location.pathname.includes('/dashboard') ||
    window.location.pathname.includes('/empresa/')
  ) {
    return;
  }

  // Remover classe de página de empresa se estivermos no dashboard geral
  document.body.classList.remove('dashboard-empresa-page');

  // Carregar dados do dashboard
  carregarEstatisticasDashboard();
  carregarEmpresasDashboard();

  // Configurar event listeners para filtros
  setupFiltrosDashboard();
}

/**
 * Configura os filtros do dashboard
 */
function setupFiltrosDashboard() {
  // Event listener para mudança de ano
  const yearSelect = document.getElementById('year-select');
  if (yearSelect) {
    yearSelect.addEventListener('change', function () {
      carregarEstatisticasDashboard();
      carregarEmpresasDashboard();
    });
  }

  // Event listener para mudança de mês
  const monthSelect = document.getElementById('month-select');
  if (monthSelect) {
    monthSelect.addEventListener('change', function () {
      carregarEstatisticasDashboard();
      carregarEmpresasDashboard();
    });
  }
}

/**
 * Carrega as estatísticas do dashboard principal
 */
function carregarEstatisticasDashboard() {
  const year =
    document.getElementById('year-select')?.value || new Date().getFullYear();
  const month =
    document.getElementById('month-select')?.value || new Date().getMonth() + 1;

  console.log('Carregando estatísticas do dashboard...', { year, month });

  // Mostrar loading nos cards
  mostrarLoadingCards();

  // Fazer requisição para a API
  fetch(`/api/dashboard/estatisticas?year=${year}&month=${month}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      console.log('Estatísticas recebidas:', data);

      if (data.success) {
        dashboardData = data.estatisticas;
        atualizarCardsDashboard(data.estatisticas);
      } else {
        console.error('Erro ao carregar estatísticas:', data.message);
        mostrarErroCards();
      }
    })
    .catch((error) => {
      console.error('Erro na requisição de estatísticas:', error);
      mostrarErroCards();
    });
}

/**
 * Carrega a lista de empresas do dashboard
 */
function carregarEmpresasDashboard() {
  const year =
    document.getElementById('year-select')?.value || new Date().getFullYear();
  const month =
    document.getElementById('month-select')?.value || new Date().getMonth() + 1;

  console.log('Carregando empresas do dashboard...', { year, month });

  // Mostrar loading na lista de empresas
  mostrarLoadingEmpresas();

  // Fazer requisição para a API
  fetch(`/api/dashboard/empresas?year=${year}&month=${month}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      console.log('Empresas recebidas:', data);

      if (data.success) {
        empresasData = data.empresas;
        atualizarListaEmpresas(data.empresas);
      } else {
        console.error('Erro ao carregar empresas:', data.message);
        mostrarErroEmpresas();
      }
    })
    .catch((error) => {
      console.error('Erro na requisição de empresas:', error);
      mostrarErroEmpresas();
    });
}

/**
 * Atualiza os cards do dashboard com as estatísticas
 */
function atualizarCardsDashboard(estatisticas) {
  // Card 1: Total de empresas
  const totalEmpresasElement = document.getElementById('total-empresas');
  if (totalEmpresasElement) {
    totalEmpresasElement.textContent = estatisticas.total_empresas;
  }

  // Card 2: Empresas auditadas
  const empresasAuditadasElement =
    document.getElementById('empresas-auditadas');
  if (empresasAuditadasElement) {
    empresasAuditadasElement.textContent = estatisticas.empresas_auditadas;
  }

  // Card 3: Empresas pendentes
  const empresasPendentesElement =
    document.getElementById('empresas-pendentes');
  if (empresasPendentesElement) {
    empresasPendentesElement.textContent = estatisticas.empresas_pendentes;
  }

  console.log('Cards atualizados com sucesso');
}

/**
 * Atualiza a lista de empresas
 */
function atualizarListaEmpresas(empresas) {
  const container = document.getElementById('empresas-list-container');
  if (!container) {
    console.error('Container da lista de empresas não encontrado');
    return;
  }

  if (empresas.length === 0) {
    container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Nenhuma empresa encontrada para o período selecionado.
            </div>
        `;
    return;
  }

  // Gerar HTML da lista de empresas
  let html = '<div class="row">';

  empresas.forEach((empresa) => {
    const statusClass = getStatusClass(empresa.status_auditoria);
    const statusText = getStatusText(empresa.status_auditoria);
    const progressPercent = Math.round(empresa.progresso_auditoria);

    html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card empresa-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title mb-0">${
                              empresa.razao_social
                            }</h5>
                            <span class="badge ${statusClass}">${statusText}</span>
                        </div>

                        <p class="card-text text-muted small mb-3">
                            CNPJ: ${formatCNPJ(empresa.cnpj)}
                        </p>

                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar ${statusClass.replace(
                              'badge-',
                              'bg-',
                            )}"
                                 role="progressbar"
                                 style="width: ${progressPercent}%"
                                 aria-valuenow="${progressPercent}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                            </div>
                        </div>

                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <small class="text-muted">Auditados</small>
                                <div class="fw-bold">${
                                  empresa.tributos_auditados.length
                                }/6</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">Inconsistências</small>
                                <div class="fw-bold text-danger">${
                                  empresa.total_inconsistencias
                                }</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">Valor</small>
                                <div class="fw-bold">${formatCurrency(
                                  empresa.total_valor_inconsistente,
                                )}</div>
                            </div>
                        </div>

                        <button class="btn btn-primary btn-sm w-100"
                                onclick="navegarParaDashboardEmpresa(${
                                  empresa.id
                                })">
                            <i class="fas fa-chart-line"></i> Ver Detalhes
                        </button>
                    </div>
                </div>
            </div>
        `;
  });

  html += '</div>';
  container.innerHTML = html;

  console.log('Lista de empresas atualizada com sucesso');
}

/**
 * Navega para o dashboard específico da empresa
 */
function navegarParaDashboardEmpresa(empresaId) {
  console.log('Navegando para dashboard da empresa:', empresaId);

  // Salvar empresa selecionada
  localStorage.setItem('selectedCompany', empresaId);

  // Navegar para a página específica da empresa
  window.location.href = `/dashboard/empresa/${empresaId}`;
}

/**
 * Funções auxiliares
 */
function getStatusClass(status) {
  switch (status) {
    case 'completa':
      return 'badge-success';
    case 'parcial':
      return 'badge-warning';
    case 'pendente':
      return 'badge-secondary';
    default:
      return 'badge-secondary';
  }
}

function getStatusText(status) {
  switch (status) {
    case 'completa':
      return 'Completa';
    case 'parcial':
      return 'Parcial';
    case 'pendente':
      return 'Pendente';
    default:
      return 'Desconhecido';
  }
}

function formatCNPJ(cnpj) {
  if (!cnpj) return '';
  return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
}

function formatCurrency(value) {
  if (!value || value === 0) return 'R$ 0,00';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Funções de loading e erro
 */
function mostrarLoadingCards() {
  ['total-empresas', 'empresas-auditadas', 'empresas-pendentes'].forEach(
    (id) => {
      const element = document.getElementById(id);
      if (element) {
        element.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
      }
    },
  );
}

function mostrarErroCards() {
  ['total-empresas', 'empresas-auditadas', 'empresas-pendentes'].forEach(
    (id) => {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = 'Erro';
      }
    },
  );
}

function mostrarLoadingEmpresas() {
  const container = document.getElementById('empresas-list-container');
  if (container) {
    container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Carregando empresas...</p>
            </div>
        `;
  }
}

function mostrarErroEmpresas() {
  const container = document.getElementById('empresas-list-container');
  if (container) {
    container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                Erro ao carregar lista de empresas. Tente novamente.
            </div>
        `;
  }
}

// Inicializar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function () {
  // Aguardar um pouco para garantir que outros scripts foram carregados
  setTimeout(initNovoDashboard, 500);
});

// Exportar funções para uso global
window.initNovoDashboard = initNovoDashboard;
window.navegarParaDashboardEmpresa = navegarParaDashboardEmpresa;
